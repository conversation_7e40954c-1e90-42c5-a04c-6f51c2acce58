<div class="h-[calc(100vh - 74px)] w-full bg-[var(--background-light-gray)]" style="height: calc(100vh - 74px)">
  <!-- Main content with splitter -->
  <as-split direction="horizontal" [gutterSize]="2" [useTransition]="true" (dragEnd)="onSplitDragEnd($event)"
    (dragProgress)="onSplitDragProgress($event)" (gutterDblClick)="onGutterDoubleClick($event)">
    <!-- Main Chat Container -->
    <as-split-area [size]="mainContentSplitSize" [minSize]="50" class="main-content-area">
      <div class="flex flex-col h-full transition-all duration-300 ease-in-out">
        <!-- Chat Messages -->
        <div #chatContainer (scroll)="onChatScroll()" class="flex-1 overflow-y-auto px-2 py-4 sm:px-4 sm:py-6">
          <div class="w-[90%] mx-auto">
            <!-- Loading more indicator at the top -->
            <div *ngIf="isLoadingMore" class="flex justify-center items-center py-4 mb-4">
              <div class="flex items-center gap-2 text-[var(--text-medium-gray)]">
                <div class="flex gap-1">
                  <span
                    class="w-2 h-2 bg-[var(--text-medium-gray)] rounded-full animate-bounce [animation-delay:-0.3s]"></span>
                  <span
                    class="w-2 h-2 bg-[var(--text-medium-gray)] rounded-full animate-bounce [animation-delay:-0.15s]"></span>
                  <span class="w-2 h-2 bg-[var(--text-medium-gray)] rounded-full animate-bounce"></span>
                </div>
                <span class="text-sm">Loading more messages...</span>
              </div>
            </div>

            <!-- End of data message -->
            <div *ngIf="!hasMoreData && !isLoadingMore && totalHistoryCount > pageSize && currentPage > 1"
              class="flex justify-center items-center py-4 mb-4">
              <div class="text-center">
                <p class="text-[var(--text-medium-gray)] text-sm">
                  <i class="ri-check-line mr-1"></i>
                  You've viewed all chat history
                </p>
                <p class="text-[var(--text-medium-gray)] text-xs mt-1">
                  {{totalHistoryCount}} message{{totalHistoryCount === 1 ? '' : 's'}} total
                </p>
              </div>
            </div>

            <!-- Welcome Message when no chat history -->
            <div *ngIf="hasNoHistories" class="mb-6 sm:mb-8 md:mb-10">
              <div class="flex flex-col items-center justify-center p-4 sm:p-6">
                <!-- Message when no chat histories -->
                <div *ngIf="selectedAgent" class="text-center mb-6">
                  <p class="text-[var(--text-medium-gray)] text-base">
                    No chat history found for
                    <span class="font-semibold text-[var(--primary-purple)]">{{
                      selectedAgent
                      }}</span>
                  </p>
                  <p class="text-[var(--text-medium-gray)] text-sm mt-2">
                    Start a conversation by sending a message below.
                  </p>
                </div>
              </div>
            </div>

            <div *ngFor="let message of currentConversation?.histories" class="flex flex-col gap-4">
              <!-- User Message -->
              <div
                class="flex group gap-2 sm:gap-3 items-start justify-start bg-[var(--background-white)] rounded-[8px] shadow-[var(--box-shadow)] px-2 py-2 sm:px-4 sm:py-3">
                <div
                  class="h-8 w-8 sm:h-10 sm:w-10 bg-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] flex items-center justify-center">
                  <i class="ri-user-2-fill text-lg sm:text-xl text-[var(--primary-purple)]"></i>
                </div>
                <div class="flex flex-col w-full">
                  <span class="text-[var(--text-medium-gray)] text-xs sm:text-sm px-1">You • just now</span>
                  <div class="flex gap-1 justify-between" *ngIf="!message.editingMode">
                    <div
                      class="text-sm sm:text-[15px] md:text-[16px] text-[var(--text-dark)] p-2 sm:p-3 rounded-[8px] shadow-[var(--box-shadow)]">
                      <span #messageText>{{ message.question }}</span>
                    </div>
                    <button
                      class="p-2 outline-none border-none h-8 w-8 sm:h-10 sm:w-10 rounded-[var(--border-radius-small)] bg-[var(--hover-blue-gray)] opacity-0 group-hover:opacity-100 transition-all duration-300 ease-in-out hover:bg-[var(--secondary-purple)] mt-2 flex items-center justify-center cursor-pointer text-[var(--text-medium-gray)] hover:text-[var(--primary-purple)]"
                      (click)="
                        message.editingMode = !message.editingMode;
                        previousResponse = message.question || ''
                      ">
                      <i class="ri-pencil-line text-lg sm:text-xl"></i>
                    </button>
                  </div>
                  <div
                    class="editmode flex items-center bg-[var(--hover-blue-gray)] p-2 sm:p-3 rounded-2xl w-full relative"
                    *ngIf="message.editingMode">
                    <textarea type="text" [(ngModel)]="message.question" placeholder="What can I help you with, {{
                        auth.getUserName() || 'there'
                      }}? Type @ to mention an agent"
                      class="outline-none resize-none bg-transparent px-3 sm:px-4 border-none text-sm sm:text-[16px] md:text-[17px] flex-1 min-h-[80px] sm:min-h-[100px] max-h-[150px] sm:max-h-[200px] line-height-[var(--line-height)] text-[var(--text-dark)] placeholder-[var(--text-medium-gray)]"
                      (input)="adjustInputHeight()" (keydown)="handleKeyDown($event)"></textarea>
                    <div class="flex flex-col sm:flex-row gap-2 absolute right-2 bottom-2 sm:right-3 sm:bottom-3">
                      <button (click)="
                          message.editingMode = false;
                          message.question = previousResponse
                        "
                        class="px-3 py-1 sm:px-4 sm:py-2 bg-[var(--text-dark)] hover:bg-[#2F2F2F] transition-[var(--transition-default)] text-xs sm:text-sm rounded-xl outline-none border-none cursor-pointer text-[var(--background-white)] min-h-[40px]">
                        Cancel
                      </button>
                      <button (click)="editMessage(message)"
                        class="px-3 py-1 sm:px-4 sm:py-2 bg-[var(--background-white)] hover:bg-[var(--hover-blue-gray)] text-xs sm:text-sm rounded-xl outline-none border-none cursor-pointer min-h-[40px] text-[var(--text-dark)]">
                        Send
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <!-- AI Response -->
              <div class="flex w-full mb-4">
                <!-- Show loading state when message is loading -->
                <ng-container *ngIf="message.isLoading">
                  <div class="flex-1 group w-full">
                    <div
                      class="bg-[var(--background-white)] rounded-[8px] shadow-[var(--box-shadow)] px-2 py-2 sm:px-4 sm:py-3">
                      <!-- Header: Icon and Timestamp -->
                      <div class="flex gap-2 w-full">
                        <div
                          class="flex items-center justify-center w-[40px] h-[40px] bg-[var(--primary-purple)] rounded-[var(--border-radius-small)]">
                          <i class="ri-sparkling-2-fill text-[var(--background-white)] text-lg sm:text-xl"></i>
                        </div>
                        <div class="flex justify-between mb-1" style="width: calc(100% - 40px)">
                          <div class="flex justify-center px-2 gap-2 flex-col w-full">
                            <div
                              class="text-[var(--text-medium-gray)] text-xs sm:text-sm px-1 flex items-start gap-1 justify-between">
                              <div class="flex gap-[1px] flex-col justify-center">
                                <div>
                                  <span class="text-[var(--text-dark)]">{{
                                    currentConversation.agentName ||
                                    "Assistant"
                                    }}
                                  </span>
                                </div>
                                <div>
                                  {{ selectedWorkspace || "Workspace" }}
                                </div>
                              </div>
                            </div>
                            <!-- Loading indicator -->
                            <div class="text-sm sm:text-[15px] md:text-[16px] text-[var(--text-dark)]">
                              <div class="flex items-center gap-2 text-[var(--text-medium-gray)]">
                                <div class="flex gap-1">
                                  <span
                                    class="w-2 h-2 bg-[var(--text-medium-gray)] rounded-full animate-bounce [animation-delay:-0.3s]"></span>
                                  <span
                                    class="w-2 h-2 bg-[var(--text-medium-gray)] rounded-full animate-bounce [animation-delay:-0.15s]"></span>
                                  <span class="w-2 h-2 bg-[var(--text-medium-gray)] rounded-full animate-bounce"></span>
                                </div>
                                <span class="text-xs sm:text-sm">Generating response...</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </ng-container>

                <!-- Show streaming state when message is streaming -->
                <ng-container *ngIf="isMessageStreaming(message)">
                  <div class="flex-1 group w-full">
                    <div
                      class="bg-[var(--background-white)] rounded-[8px] shadow-[var(--box-shadow)] px-2 py-2 sm:px-4 sm:py-3">
                      <!-- Header: Icon and Timestamp -->
                      <div class="flex gap-2 w-full">
                        <div
                          class="flex items-center justify-center w-[40px] h-[40px] bg-[var(--primary-purple)] rounded-[var(--border-radius-small)]">
                          <i class="ri-sparkling-2-fill text-[var(--background-white)] text-lg sm:text-xl"></i>
                        </div>
                        <div class="flex justify-between mb-1" style="width: calc(100% - 40px)">
                          <div class="flex justify-center px-2 gap-2 flex-col w-full">
                            <div
                              class="text-[var(--text-medium-gray)] text-xs sm:text-sm px-1 flex items-start gap-1 justify-between">
                              <div class="flex gap-[1px] flex-col justify-center">
                                <div>
                                  <span class="text-[var(--text-dark)]">{{
                                    currentConversation.agentName ||
                                    "Assistant"
                                    }}
                                  </span>
                                </div>
                                <div>
                                  {{ selectedWorkspace || "Workspace" }}
                                </div>
                              </div>
                            </div>
                            <div class="text-sm sm:text-[15px] md:text-[16px] text-[var(--text-dark)]">
                              <!-- Streaming response content with layout detection -->
                              <div class="response-container">
                                <!-- SQL View for streaming -->
                                <div *ngIf="getResponseLayoutType(getCurrentResponse(message)) === 'sqlView'" class="sql-view-container">
                                  <markdown
                                    [data]="hasSqlContent(getCurrentResponse(message)?.responseText || '') ? extractNonSqlContent(getCurrentResponse(message)?.responseText || '') : (getCurrentResponse(message)?.responseText || '')">
                                  </markdown>
                                  <div *ngIf="hasSqlContent(getCurrentResponse(message)?.responseText || '')" class="sql-code-container mt-4 mb-4 relative">
                                    <div class="bg-[#1E1E1E] rounded-md overflow-hidden">
                                      <div class="flex justify-between items-center px-3 py-2 bg-[#2A2A2A] border-b border-[#333333]">
                                        <span class="text-white text-xs font-medium">SQL Query</span>
                                        <div class="flex gap-2">
                                          <button (click)="copySqlContent(extractSqlContent(getCurrentResponse(message)?.responseText || ''))"
                                            class="text-xs bg-[#333333] hover:bg-[#444444] text-white px-2 py-1 rounded flex items-center gap-1 transition-colors">
                                            <i class="ri-file-copy-line text-xs"></i>
                                            <span>Copy</span>
                                          </button>
                                          <button (click)="openSqlConnectionDialog(extractSqlContent(getCurrentResponse(message)?.responseText || ''))"
                                            class="text-xs bg-[#0078D4] hover:bg-[#106EBE] text-white px-2 py-1 rounded flex items-center gap-1 transition-colors">
                                            <i class="ri-play-fill text-xs"></i>
                                            <span>Run SQL</span>
                                          </button>
                                        </div>
                                      </div>
                                      <div class="p-3 overflow-x-auto max-w-full">
                                        <pre class="text-white text-sm font-mono whitespace-pre"><code class="language-sql">{{extractSqlContent(getCurrentResponse(message)?.responseText || '')}}</code></pre>
                                      </div>
                                    </div>
                                  </div>
                                </div>

                                <!-- Blog View for streaming -->
                                <div *ngIf="getResponseLayoutType(getCurrentResponse(message)) === 'blogView'" class="blog-view-container">
                                  <div class="blog-content bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded-[var(--border-radius-large)]">
                                    <div class="blog-header relative bg-gradient-to-r from-[var(--primary-purple)] to-[var(--secondary-purple)] px-8 py-6">
                                      <div class="relative z-10">
                                        <h1 class="text-2xl font-bold text-white leading-tight mb-2">
                                          {{ extractBlogTitle(getCurrentResponse(message)?.responseText || '') }}
                                        </h1>
                                        <div class="flex items-center gap-4 text-white/80 text-sm">
                                          <div class="flex items-center gap-1">
                                            <i class="ri-user-line"></i>
                                            <span>{{ currentConversation.agentName || 'AI Assistant' }}</span>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                    <div class="blog-body p-8">
                                      <div class="markdown-blog prose prose-lg max-w-none">
                                        <markdown [data]="getCurrentResponse(message)?.responseText || ''"></markdown>
                                      </div>
                                    </div>
                                  </div>
                                </div>

                                <!-- Email View for streaming -->
                                <div *ngIf="getResponseLayoutType(getCurrentResponse(message)) === 'emailView'" class="email-view-container">
                                  <div class="email-content border border-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] shadow-[var(--box-shadow)] overflow-hidden">
                                    <div class="p-5">
                                      <div class="form-header -m-5 p-5 mb-6">
                                        <h2 class="text-lg font-semibold text-[var(--background-white)]">
                                          <i class="ri-mail-send-line mr-2"></i>Composing Email...
                                        </h2>
                                      </div>
                                      <div class="markdown-email">
                                        <markdown [data]="getCurrentResponse(message)?.responseText || ''"></markdown>
                                      </div>
                                    </div>
                                  </div>
                                </div>

                                <!-- Default View for streaming -->
                                <div *ngIf="getResponseLayoutType(getCurrentResponse(message)) !== 'sqlView' && getResponseLayoutType(getCurrentResponse(message)) !== 'blogView' && getResponseLayoutType(getCurrentResponse(message)) !== 'emailView'">
                                  <markdown [data]="getCurrentResponse(message)?.responseText || ''"></markdown>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </ng-container>

                <!-- Show actual response when available -->
                <ng-container *ngIf="
                    !message.isLoading &&
                    !isMessageStreaming(message) &&
                    message.responses &&
                    message.responses.length > 0
                  ">
                  <div class="flex-1 group w-full">
                    <!-- Current response container -->
                    <div
                      class="bg-[var(--background-white)] rounded-[8px] shadow-[var(--box-shadow)] px-2 py-2 sm:px-4 sm:py-3">
                      <!-- Header: Icon and Timestamp -->
                      <div class="flex gap-2 w-full">
                        <div
                          class="flex items-center justify-center w-[40px] h-[40px] bg-[var(--primary-purple)] rounded-[var(--border-radius-small)]">
                          <i class="ri-sparkling-2-fill text-[var(--background-white)] text-lg sm:text-xl"></i>
                        </div>
                        <div class="flex justify-between mb-1" style="width: calc(100% - 40px)">
                          <div class="flex justify-center px-2 gap-2 flex-col w-full">
                            <div
                              class="text-[var(--text-medium-gray)] text-xs sm:text-sm px-1 flex items-start gap-1 justify-between">
                              <div class="flex gap-[1px] flex-col justify-center">
                                <div>
                                  <span class="text-[var(--text-dark)]">{{
                                    currentConversation.agentName ||
                                    "Assistant"
                                    }}
                                  </span>
                                </div>
                                <div>
                                  {{ selectedWorkspace || "Workspace" }}
                                </div>
                              </div>
                            </div>
                            <div class="text-sm sm:text-[15px] md:text-[16px] text-[var(--text-dark)]">
                              <!-- Response content with layout detection -->
                              <div class="response-container">
                                <!-- SQL View - Only show when agent is SQL-related -->
                                <div *ngIf="getResponseLayoutType(getCurrentResponse(message)) === 'sqlView'" class="sql-view-container">
                                  <!-- Regular markdown content -->
                                  <markdown
                                    [data]="hasSqlContent(getCurrentResponse(message)?.responseText || '') ? extractNonSqlContent(getCurrentResponse(message)?.responseText || '') : (getCurrentResponse(message)?.responseText || '')">
                                  </markdown>

                                  <!-- SQL Code Box (only shown if SQL content exists) -->
                                  <div *ngIf="hasSqlContent(getCurrentResponse(message)?.responseText || '')" class="sql-code-container mt-4 mb-4 relative">
                                    <div class="bg-[#1E1E1E] rounded-md overflow-hidden">
                                      <div
                                        class="flex justify-between items-center px-3 py-2 bg-[#2A2A2A] border-b border-[#333333]">
                                        <span class="text-white text-xs font-medium">SQL Query</span>
                                        <div class="flex gap-2">
                                          <button (click)="copySqlContent(extractSqlContent(getCurrentResponse(message)?.responseText || ''))"
                                            class="text-xs bg-[#333333] hover:bg-[#444444] text-white px-2 py-1 rounded flex items-center gap-1 transition-colors">
                                            <i class="ri-file-copy-line text-xs"></i>
                                            <span>Copy</span>
                                          </button>
                                          <button
                                            (click)="openSqlConnectionDialog(extractSqlContent(getCurrentResponse(message)?.responseText || ''))"
                                            class="text-xs bg-[#0078D4] hover:bg-[#106EBE] text-white px-2 py-1 rounded flex items-center gap-1 transition-colors">
                                            <i class="ri-play-fill text-xs"></i>
                                            <span>Run SQL</span>
                                          </button>
                                        </div>
                                      </div>
                                      <div class="p-3 overflow-x-auto max-w-full">
                                        <pre
                                          class="text-white text-sm font-mono whitespace-pre"><code class="language-sql">{{extractSqlContent(getCurrentResponse(message)?.responseText || '')}}</code></pre>
                                      </div>
                                    </div>
                                  </div>
                                </div>

                                <!-- Blog View - Only show when agent is Blog-related -->
                                <div *ngIf="getResponseLayoutType(getCurrentResponse(message)) === 'blogView'" class="blog-view-container">
                                  <div
                                    class="blog-content bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded-[var(--border-radius-large)]">

                                    <!-- Blog Header with Gradient -->
                                    <div
                                      class="blog-header relative bg-gradient-to-r from-[var(--primary-purple)] to-[var(--secondary-purple)] px-8 py-6">
                                      <!-- Decorative Pattern -->
                                      <div class="absolute inset-0 opacity-10">
                                        <div class="absolute top-4 right-4 w-20 h-20 border-2 border-white rounded-full">
                                        </div>
                                        <div
                                          class="absolute bottom-4 left-4 w-16 h-16 border border-white rounded-lg rotate-45">
                                        </div>
                                        <div class="absolute top-1/2 right-1/3 w-8 h-8 bg-white rounded-full opacity-20">
                                        </div>
                                      </div>

                                      <!-- Header Content -->
                                      <div class="relative z-10">
                                        <div class="flex items-start justify-between mb-3">
                                          <div class="flex items-center gap-2 text-white/80 text-sm">
                                            <div class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                                              <i class="ri-article-line text-white"></i>
                                            </div>
                                            <span class="font-medium">Blog Post</span>
                                          </div>
                                          <div
                                            class="flex items-center gap-1 text-white/70 text-xs bg-white/10 px-2 py-1 rounded-full">
                                            <i class="ri-time-line"></i>
                                            <span>{{ message.timestamp.toJSDate() | date:'MMM d, y' }}</span>
                                          </div>
                                        </div>

                                        <h1 class="text-2xl font-bold text-white leading-tight mb-2">
                                          {{ extractBlogTitle(getCurrentResponse(message)?.responseText || '') }}
                                        </h1>

                                        <div class="flex items-center gap-4 text-white/80 text-sm">
                                          <div class="flex items-center gap-1">
                                            <i class="ri-user-line"></i>
                                            <span>{{ currentConversation.agentName || 'AI Assistant' }}</span>
                                          </div>
                                          <div class="flex items-center gap-1">
                                            <i class="ri-calendar-line"></i>
                                            <span>{{ message.timestamp.toJSDate() | date:'shortTime' }}</span>
                                          </div>
                                        </div>
                                      </div>
                                    </div>

                                    <!-- Blog Content with Enhanced Styling -->
                                    <div class="blog-body p-8">
                                      <div class="markdown-blog prose prose-lg max-w-none">
                                        <markdown [data]="getCurrentResponse(message)?.responseText || ''"></markdown>
                                      </div>
                                    </div>

                                    <!-- Enhanced Blog Footer -->
                                    <div
                                      class="blog-footer bg-gradient-to-r from-[var(--hover-blue-gray)] to-[var(--background-light-gray)] px-8 py-4 border-t border-[var(--hover-blue-gray)]">
                                      <div class="flex items-center justify-between">
                                        <!-- Left Side - Engagement Stats -->
                                        <div class="flex items-center gap-6 text-[var(--text-medium-gray)] text-sm">
                                          <div class="flex items-center gap-1">
                                            <i class="ri-eye-line"></i>
                                            <span>Ready to share</span>
                                          </div>
                                          <div class="flex items-center gap-1">
                                            <i class="ri-file-text-line"></i>
                                            <span>{{ ((getCurrentResponse(message)?.responseText || '').length / 5) | number:'1.0-0' }} words</span>
                                          </div>
                                        </div>

                                        <!-- Right Side - Action Buttons -->
                                        <div class="flex gap-3">
                                          <button (click)="copyBlogContent(getCurrentResponse(message)?.responseText || '')"
                                            class="group bg-[var(--primary-purple)] text-[var(--background-white)] rounded-[6px] px-3 py-1.5 sm:px-4 sm:py-2 cursor-pointer hover:bg-[var(--secondary-purple)] transition-[var(--transition-default)] disabled:opacity-50 border-none outline-none flex items-center gap-1 min-h-[40px] sm:min-h-[48px]">
                                            <i
                                              class="ri-file-copy-line text-sm group-hover:scale-110 transition-transform"></i>
                                            <span class="font-medium">Copy</span>
                                          </button>

                                          <button (click)="openBlogShareDialog(getCurrentResponse(message)?.responseText || '')"
                                            class="group bg-[var(--primary-purple)] text-[var(--background-white)] rounded-[6px] px-3 py-1.5 sm:px-4 sm:py-2 cursor-pointer hover:bg-[var(--secondary-purple)] transition-[var(--transition-default)] disabled:opacity-50 border-none outline-none flex items-center gap-1 min-h-[40px] sm:min-h-[48px]">
                                            <i class="ri-share-line text-sm group-hover:scale-110 transition-transform"></i>
                                            <span class="font-medium">Share</span>
                                          </button>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>

                                <!-- Email View - Only show when agent is Email-related -->
                                <div *ngIf="getResponseLayoutType(getCurrentResponse(message)) === 'emailView'" class="email-view-container">
                                  <div
                                    class="email-content border border-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] shadow-[var(--box-shadow)] overflow-hidden">
                                    <!-- Email Form -->
                                    <form class="p-5">
                                      <!-- Email Header -->
                                      <div class="form-header -m-5 p-5 mb-6">
                                        <div class="flex justify-between items-center">
                                          <h2 class="text-lg font-semibold text-[var(--background-white)]">
                                            <i class="ri-mail-send-line mr-2"></i>Compose Email
                                          </h2>
                                          <div class="flex items-center">
                                            <span class="text-sm text-white opacity-80">AI-generated draft ready to
                                              edit</span>
                                          </div>
                                        </div>
                                      </div>
                                      <!-- Subject Field -->
                                      <div class="mb-4">
                                        <div class="flex items-center mb-1">
                                          <label class="block text-sm font-medium text-[var(--text-dark)]"
                                            for="emailSubject">Subject</label>
                                          <div class="ml-2 text-xs text-[var(--text-medium-gray)]">(required)</div>
                                        </div>
                                        <input type="text" id="emailSubject" #emailSubject
                                          [value]="extractEmailSubject(getCurrentResponse(message)?.responseText || '')"
                                          class="w-full p-2 rounded-[var(--border-radius-small)] focus:outline-none focus:border-[var(--primary-purple)]"
                                          placeholder="Enter email subject" />
                                      </div>

                                      <!-- To Field -->
                                      <div class="mb-4">
                                        <div class="flex items-center mb-1">
                                          <label class="block text-sm font-medium text-[var(--text-dark)]"
                                            for="emailTo">To</label>
                                          <div class="ml-2 text-xs text-[var(--text-medium-gray)]">(required)</div>
                                        </div>
                                        <div class="relative">
                                          <i
                                            class="ri-user-line absolute left-3 top-[28%] transform -translate-y-1/2 text-[var(--text-medium-gray)]"></i>
                                          <input type="text" id="emailTo" #emailTo [value]="extractEmailTo(getCurrentResponse(message)?.responseText || '')"
                                            class="w-full p-2 pl-8 rounded-[var(--border-radius-small)] focus:outline-none focus:border-[var(--primary-purple)]"
                                            placeholder="<EMAIL> (for multiple use CC field)" />
                                        </div>
                                      </div>

                                      <!-- CC Field -->
                                      <div class="mb-4">
                                        <div class="flex items-center justify-between mb-1">
                                          <div class="flex items-center">
                                            <label class="block text-sm font-medium text-[var(--text-dark)]"
                                              for="emailCc">CC</label>
                                            <div class="ml-2 text-xs text-[var(--text-medium-gray)]">(optional)</div>
                                          </div>
                                        </div>
                                        <div class="relative">
                                          <i
                                            class="ri-user-shared-line absolute left-3 top-[28%] transform -translate-y-1/2 text-[var(--text-medium-gray)]"></i>
                                          <input type="text" id="emailCc" #emailCc [value]="extractEmailCc(getCurrentResponse(message)?.responseText || '')"
                                            class="w-full p-2 pl-8 rounded-[var(--border-radius-small)] focus:outline-none focus:border-[var(--primary-purple)]"
                                            placeholder="<EMAIL>, <EMAIL>" />
                                        </div>
                                      </div>

                                      <!-- Email Body -->
                                      <div class="mb-4">
                                        <div class="flex items-center justify-between mb-1">
                                          <label class="block text-sm font-medium text-[var(--text-dark)]"
                                            for="emailBody">Message</label>
                                          <button type="button"
                                            class="text-xs text-[var(--primary-purple)] hover:text-[var(--secondary-purple)]"
                                            (click)="emailBody.value = extractEmailBody(getCurrentResponse(message)?.responseText || '')">
                                            <i class="ri-refresh-line"></i> Reset to AI version
                                          </button>
                                        </div>
                                        <textarea id="emailBody" #emailBody [value]="extractEmailBody(getCurrentResponse(message)?.responseText || '')"
                                          class="w-full p-3 rounded-[var(--border-radius-small)] focus:outline-none focus:border-[var(--primary-purple)] min-h-[180px] font-normal"
                                          placeholder="Enter your message here..."></textarea>
                                      </div>
                                    </form>
                                    <!-- Email Footer with Actions -->
                                    <div class="form-footer px-5 py-3 flex justify-between items-center">
                                      <div class="text-[var(--text-medium-gray)] text-sm flex items-center">
                                        <i class="ri-information-line mr-1"></i>
                                        <span>Your email will be sent with HTML formatting for better readability.</span>
                                      </div>
                                      <div class="flex gap-2">
                                        <button type="button"
                                          (click)="copyEmailContent(emailSubject.value, emailTo.value, emailCc.value, emailBody.value)"
                                          class="copy-button text-sm px-3 py-2 rounded-[var(--border-radius-small)] flex items-center gap-1 transition-[var(--transition-default)] hover:bg-[var(--hover-blue-gray)]">
                                          <i class="ri-file-copy-line text-sm"></i>
                                          <span>Copy All</span>
                                        </button>
                                        <button type="button"
                                          (click)="sendEmail(emailSubject.value, emailTo.value, emailCc.value, emailBody.value)"
                                          class="text-sm bg-[var(--primary-purple)] hover:bg-[var(--secondary-purple)] transition-[var(--transition-default)] text-[var(--background-white)] px-3 py-2 rounded-[var(--border-radius-small)] flex items-center gap-1">
                                          <i class="ri-send-plane-fill text-sm"></i>
                                          <span>Send Email</span>
                                        </button>
                                      </div>
                                    </div>
                                  </div>
                                </div>

                                <!-- Default View for other response types -->
                                <div
                                  *ngIf="getResponseLayoutType(getCurrentResponse(message)) !== 'sqlView' && getResponseLayoutType(getCurrentResponse(message)) !== 'blogView' && getResponseLayoutType(getCurrentResponse(message)) !== 'emailView'">
                                  <markdown [data]="getCurrentResponse(message)?.responseText || ''"></markdown>
                                </div>
                              </div>

                              <!-- Chat Sources Section -->
                              <div class="flex items-center gap-2 mt-3"
                                *ngIf="getParsedChatSources(getCurrentResponse(message)).length > 0">
                                <!-- Web Search Indicator (Clickable) -->
                                <div
                                  (click)="toggleSearchResultsSidebar(chatSource.chatSourceDescriptions, chatSource.source)"
                                  *ngFor="let chatSource of getParsedChatSources(getCurrentResponse(message))"
                                  class="flex items-center bg-[#1E1E1E] text-white text-xs rounded-full px-2 py-1.5 border border-[#333333] cursor-pointer hover:bg-[#2A2A2A] transition-all duration-200"
                                  title="Click to view {{chatSource.source}} references">
                                  <i class="ri-global-line mr-1"></i>
                                  <span>{{ chatSource.chatSourceDescriptions?.length || 0 }}
                                    {{chatSource.source}}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Action Buttons Section with Navigation -->
                      <div class="flex justify-start items-center mt-3">
                        <div
                          class="flex flex-wrap items-center gap-1 p-1 rounded-md bg-[var(--background-white)] shadow-sm border border-[var(--hover-blue-gray)] *:border-none *:outline-none *:bg-transparent *:text-[16px] sm:*:text-[18px] *:cursor-pointer">
                          <!-- Navigation buttons -->
                          <button *ngIf="message.responses.length > 1"
                            class="p-1 hover:bg-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] transition-[var(--transition-default)]"
                            (click)="goToPreviousResponse(message)" [disabled]="
                              getCurrentResponseIndex(message.id || '') === 0
                            " [ngClass]="{
                              'opacity-50':
                                getCurrentResponseIndex(message.id || '') === 0
                            }">
                            <i class="ri-arrow-left-s-line text-[var(--text-medium-gray)]"></i>
                          </button>
                          <!-- Response counter -->
                          <span *ngIf="message.responses.length > 1" class="text-[var(--text-medium-gray)]">
                            {{
                            getCurrentResponseIndex(message.id || "") + 1
                            }}/{{ message.responses.length }}
                          </span>
                          <button *ngIf="message.responses.length > 1"
                            class="p-1 hover:bg-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] transition-[var(--transition-default)]"
                            (click)="nextResponse(message)" [disabled]="
                              getCurrentResponseIndex(message.id || '') ===
                              message.responses.length - 1
                            " [ngClass]="{
                              'opacity-50':
                                getCurrentResponseIndex(message.id || '') ===
                                message.responses.length - 1
                            }">
                            <i class="ri-arrow-right-s-line text-[var(--text-medium-gray)]"></i>
                          </button>

                          <!-- Standard action buttons -->
                          <button
                            class="p-1 hover:bg-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] transition-[var(--transition-default)]"
                            (click)="
                              copyContent(
                                getCurrentResponse(message)?.responseText
                              )
                            " title="Copy to clipboard">
                            <i class="ri-file-copy-line text-[var(--text-medium-gray)]"></i>
                          </button>
                          <button
                            class="p-1 hover:bg-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] transition-[var(--transition-default)]"
                            (click)="regenerateResponse(message)" title="Regenerate response">
                            <i class="ri-restart-line text-[var(--text-medium-gray)]"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </ng-container>
              </div>
            </div>
          </div>
        </div>
        <!-- Scroll Down Arrow Button -->
        <div *ngIf="showScrollButton" (click)="scrollToBottom()"
          class="fixed cursor-pointer bottom-[208px] right-[70px] p-2 flex justify-center items-center bg-[var(--primary-purple)] rounded-full text-[var(--background-white)]"
          style="z-index: 9999">
          <i class="ri-arrow-down-s-line text-xl"></i>
        </div>
        <!-- Input Section -->
        <div class="p-2 sm:p-4 border-t border-[var(--hover-blue-gray)] relative">
          <div class="w-[90%] mx-auto">
            <div
              class="flex relative items-end justify-start bg-[var(--background-white)] rounded-[8px] px-2 py-2 sm:px-4 sm:py-3 shadow-[var(--box-shadow)]">
              <!-- Left-Side Icons -->
              <div class="w-full flex items-center gap-2 left-0">
                <div class="flex items-center gap-2 absolute left-2 sm:left-4 z-10 bottom-2">
                  <!-- Tools icon with counter -->
                  <div class="relative ml-2">
                    <div (click)="toggleAgentSidebar()"
                      class="flex bg-[var(--dialog-bg)] items-center gap-2 cursor-pointer border border-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] px-2 py-1 transition-[var(--transition-default)] hover:bg-[var(--hover-blue-gray)]"
                      title="View available agents for this workspace">
                      <i class="ri-tools-fill text-[var(--text-dark)]"></i>
                      <span
                        class="text-xs font-semibold bg-[var(--primary-purple)] text-white rounded-full px-1.5 py-0.5 min-w-[20px] text-center">
                        {{ workspaceAgents.length }}
                      </span>
                    </div>
                  </div>


                  <!-- Selected Agent Indicator -->
                  <div *ngIf="selectedAgent" class="relative">
                    <div
                      class="flex bg-[var(--dialog-bg)] items-center gap-2 cursor-pointer border border-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] px-2 py-1 transition-[var(--transition-default)] hover:bg-[var(--hover-blue-gray)] shadow-[0_0_1px_var(--shadow-color)]"
                      title="Currently selected agent">
                      <i class="ri-user-2-line text-[var(--primary-purple)]"></i>
                      <span class="font-bold text-[var(--text-dark)]">
                        {{ selectedAgent }}
                      </span>
                    </div>
                  </div>
                </div>
                <!-- Textarea -->
                <div class="flex items-center gap-2 w-full pb-[40px] sm:pb-[50px] relative">
                  <textarea #chatInput [(ngModel)]="userInput.question" (input)="adjustInputHeight()" placeholder="What can I help you with, {{
                      auth.getUserName() || 'there'
                    }}? Type @ to mention an agent"
                    class="outline-none resize-none bg-transparent px-3 sm:px-4 border-none text-sm sm:text-[16px] md:text-[17px] flex-1 min-h-[80px] sm:min-h-[100px] max-h-[150px] sm:max-h-[200px] line-height-[var(--line-height)] text-[var(--text-dark)] placeholder-[var(--text-medium-gray)]"
                    autofocus (keydown)="handleKeyDown($event)"></textarea>
                </div>
                <!-- Send Button -->
                <div class="flex items-end h-full self-end">
                  <div class="flex items-end gap-2">
                    <button (click)="sendMessage()" [disabled]="isMessageLoading"
                      class="bg-[var(--primary-purple)] text-[var(--background-white)] rounded-[6px] px-3 py-1.5 sm:px-4 sm:py-2 cursor-pointer hover:bg-[var(--secondary-purple)] transition-[var(--transition-default)] disabled:opacity-50 border-none outline-none flex items-center gap-1 min-h-[40px] sm:min-h-[48px]">
                      <span class="text-sm sm:text-[16px] md:text-[17px]">Send</span>
                      <i [class]="
                          isMessageLoading
                            ? 'ri-stop-circle-line'
                            : 'ri-send-plane-fill'
                        "></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </as-split-area>

    <!-- Right Sidebar Area - Fixed initial width of 350px -->
    <as-split-area *ngIf="showSearchResultsSidebar || isAgentSidebarOpen || isPluginSidebarOpen"
      [size]="rightSidebarSplitSize" [minSize]="15" class="right-sidebar-area">
      <div class="h-full w-full relative sidebar-container">
        <!-- Width indicator (visible during drag) -->
        <div class="width-indicator" *ngIf="isDragging">
          {{ rightSidebarWidth | number : "1.0-0" }}px
        </div>

        <!-- Source References Sidebar Component -->
        <app-source-references *ngIf="showSearchResultsSidebar" [showSidebar]="showSearchResultsSidebar"
          [searchResults]="searchResults" [currentSourceName]="currentSourceName"
          (closeSidebar)="showSearchResultsSidebar = false">
        </app-source-references>

        <!-- Agent Tools Sidebar Component -->
        <app-agent-sidebar *ngIf="isAgentSidebarOpen" [isAgentSidebarOpen]="isAgentSidebarOpen"
          [agentSidebarTitle]="agentSidebarTitle" [workspaceAgents]="workspaceAgents" (onClose)="toggleAgentSidebar()"
          (onSelectAgent)="selectAgent($event)">
        </app-agent-sidebar>

        <!-- Plugin Sidebar Component -->
        <app-plugins-sidebar *ngIf="isPluginSidebarOpen" [isPluginSidebarOpen]="isPluginSidebarOpen"
          [selectedAgent]="selectedAgent" (onClose)="onClosePluginSidebar()" (onSelectPlugin)="onSelectPlugin($event)">
        </app-plugins-sidebar>

      </div>
    </as-split-area>
  </as-split>
</div>
